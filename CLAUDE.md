# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Angular framework repository - a platform for building web applications using TypeScript. Angular is organized as a monorepo containing the core framework packages, development tools, documentation site, and browser developer tools.

## Key Commands

### Development Setup
```bash
# Install dependencies
yarn install

# Check tooling setup (important after fresh clone)
yarn check-tooling-setup
```

### Building and Testing
```bash
# Build all packages
yarn build

# Run all tests using Bazel
yarn test
bazelisk test

# Run CI test suite
yarn test:ci

# Run tests for specific packages (example)
bazelisk test //packages/core/...
bazelisk test //packages/compiler/...

# Run a single test target
bazelisk test //packages/core/test/acceptance:acceptance
```

### Code Quality and Formatting
```bash
# Run linting and format checking
yarn lint

# Run TSLint specifically
yarn tslint

# Format code using ng-dev
yarn ng-dev format changed --check
yarn ng-dev format changed --write

# Check public API changes
yarn public-api:check
yarn public-api:update

# Check for circular dependencies
yarn ts-circular-deps:check
yarn ts-circular-deps:approve
```

### Testing Specific Components
```bash
# Test devtools
yarn devtools:test

# Test with specific configuration
bazelisk test --//packages/compiler:use_template_pipeline //packages/compiler-cli/test/compliance/full
```

### Development Server and Documentation
```bash
# Run documentation development server
yarn docs

# Build documentation
yarn docs:build

# Run devtools development server
yarn devtools:devserver
```

## Architecture Overview

### Core Package Structure
- **`packages/`** - Main Angular framework packages
  - `core/` - Angular core framework (dependency injection, change detection, etc.)
  - `common/` - Common directives, pipes, and utilities (NgIf, NgFor, etc.)
  - `compiler/` - Template compiler
  - `compiler-cli/` - Angular CLI compiler
  - `router/` - Angular Router
  - `forms/` - Reactive and template-driven forms
  - `animations/` - Animation framework
  - `platform-browser/` - Browser-specific implementations
  - `platform-server/` - Server-side rendering support
  - `language-service/` - IDE support and language services
  - `service-worker/` - PWA service worker support
  - `elements/` - Angular Elements (custom elements)
  - `upgrade/` - AngularJS upgrade utilities

### Build System
- **Bazel** is the primary build system - use `bazelisk` command for all build/test operations
- Each package has its own `BUILD.bazel` file defining build targets
- Use `bazelisk test` instead of `yarn test` for individual package testing
- Bazel targets follow the pattern `//packages/package-name/...` for all targets in a package

### Development Tools
- **`tools/`** - Build tools, testing utilities, and development scripts
- **`adev/`** - New Angular documentation site (angular.dev)
- **`devtools/`** - Angular DevTools browser extension
- **`scripts/`** - Build and CI scripts

### Testing Strategy
- Unit tests: Located alongside source files, use Jasmine/Karma
- Integration tests: In `integration/` directory
- Compliance tests: In `packages/compiler-cli/test/compliance/`
- Use Bazel for running tests: `bazelisk test //path/to/test:target`

## Important Development Notes

### Commit Message Format
Follow the conventional commit format specified in CONTRIBUTING.md:
```
<type>(<scope>): <short summary>

<body>

<footer>
```

Types: `build`, `ci`, `docs`, `feat`, `fix`, `perf`, `refactor`, `test`
Scopes: Package names like `core`, `common`, `router`, `compiler`, etc.

### Code Style
- Follow Google's JavaScript Style Guide
- Wrap code at 100 characters
- Use automated formatter: `yarn ng-dev format`
- All features must be tested
- All public APIs must be documented

### Branch and PR Guidelines
- Create feature branches from `main`
- PRs should target `main` branch (not the current `17.3.x` branch)
- Run full test suite before submitting: `yarn test:ci`
- Check public API compatibility: `yarn public-api:check`
- Ensure no circular dependencies: `yarn ts-circular-deps:check`

### Special Directories
- **`goldens/`** - Golden files for API and other consistency checks
- **`integration/`** - Integration test projects
- **`modules/`** - Experimental modules and features
- **`third_party/`** - External dependencies and patches

## Troubleshooting

### Common Issues
- If builds fail after pulling changes, run `yarn install` and `yarn check-tooling-setup`
- For Bazel issues, try `bazelisk clean` and rebuild
- Windows users must use Git Bash or equivalent (not PowerShell/cmd)
- Use Node.js version specified in `.nvmrc`

### Performance Testing
- Use `yarn benchmarks` for performance benchmarking
- Payload size checks run automatically in CI

### Local Testing Against Projects
- Use `yarn build` to build packages locally
- Use `yarn link` to test changes in external projects
- Built packages are in `dist/packages-dist/`